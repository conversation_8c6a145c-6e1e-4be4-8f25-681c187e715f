@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* African-inspired color palette */
  --primary-color: #E57A44; /* Warm orange/terracotta */
  --secondary-color: #5FA777; /* Green representing nature and growth */
  --accent-color: #EFCB68; /* Gold/yellow representing wealth and hope */
  --dark-color: #472D2D; /* Earth brown */
  --light-color: #FAF3E0; /* Sand/cream color */
  --error-color: #B81D13; /* Deep red */
  --success-color: #2E8B57; /* Forest green */
}

body {
  color: var(--dark-color);
  background-color: var(--light-color);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
}

.btn-primary {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  color: white;
  transition-property: all;
  background-color: var(--primary-color);
}

.btn-primary:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  background-color: #d86a34;
}

.btn-secondary {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  color: white;
  transition-property: all;
  background-color: var(--secondary-color);
}

.btn-secondary:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  background-color: #4f9667;
}

.container-custom {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Responsive font sizes */
@media (max-width: 640px) {
  h1 {
    font-size: 1.5rem !important;
  }
  
  h2 {
    font-size: 1.25rem !important;
  }
  
  h3 {
    font-size: 1.125rem !important;
  }
  
  body {
    font-size: 0.9375rem;
  }
}

/* Amélioration de la responsivité des cartes */
@media (max-width: 640px) {
  .grid {
    gap: 1rem !important;
  }
  
  .py-16 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  
  .py-10 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  
  .py-12 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  
  .space-y-8 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 1.25rem !important;
  }
  
  .mb-8 {
    margin-bottom: 1.25rem !important;
  }
  
  .mb-10 {
    margin-bottom: 1.5rem !important;
  }
  
  .p-6 {
    padding: 1rem !important;
  }
  
  .px-6 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  .gap-4 {
    gap: 0.75rem !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Animation pour le composant CookieConsent */
@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Ajouter cette règle à la fin du fichier pour masquer l'overlay d'erreur Next.js */
div[data-nextjs-dialog-overlay],
div[data-nextjs-dialog],
div[id="__nextjs-toast-wrapper"],
.nextjs-portal {
  display: none !important;
}