<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="4 4 2485.927001953125 876.4818725585938" style="max-width: 3840px; background-color: white; max-height: 3840px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:arial,sans-serif;font-size:14px;}#my-svg p{margin:0;}#my-svg .label{font-family:arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#B8B8FF;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:1px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .node .neo-node{stroke:#B8B8FF;}#my-svg [data-look="neo"].node rect,#my-svg [data-look="neo"].cluster rect,#my-svg [data-look="neo"].node polygon{stroke:#B8B8FF;filter:drop-shadow( 1px 2px 2px rgba(185,185,185,1));}#my-svg [data-look="neo"].node path{stroke:#B8B8FF;stroke-width:1;}#my-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 1px 2px 2px rgba(185,185,185,1));}#my-svg [data-look="neo"].node .neo-line path{stroke:#B8B8FF;filter:none;}#my-svg [data-look="neo"].node circle{stroke:#B8B8FF;filter:drop-shadow( 1px 2px 2px rgba(185,185,185,1));}#my-svg [data-look="neo"].node circle .state-start{fill:#000000;}#my-svg [data-look="neo"].statediagram-cluster rect{fill:#ECECFF;stroke:#B8B8FF;stroke-width:1;}#my-svg [data-look="neo"].icon-shape .icon{fill:#B8B8FF;filter:drop-shadow( 1px 2px 2px rgba(185,185,185,1));}#my-svg [data-look="neo"].icon-shape .icon-neo path{stroke:#B8B8FF;filter:drop-shadow( 1px 2px 2px rgba(185,185,185,1));}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .user&gt;*{fill:#f9d5e5!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .user span{fill:#f9d5e5!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .frontend&gt;*{fill:#eeeeee!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .frontend span{fill:#eeeeee!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .backend&gt;*{fill:#b5ead7!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .backend span{fill:#b5ead7!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .storage&gt;*{fill:#c7ceea!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .storage span{fill:#c7ceea!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .api&gt;*{fill:#ffdac1!important;stroke:#333!important;stroke-width:2px!important;}#my-svg .api span{fill:#ffdac1!important;stroke:#333!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><g class="subgraphs"><g class="subgraph"><g data-look="classic" data-et="cluster" data-id="s1" id="s1" class="cluster"><rect height="124.96517181396484" width="556.78125" y="12" x="262.7631578947369" style=""/><g transform="translate(513.8959703947369, 12)" class="cluster-label"><foreignObject height="21" width="54.515625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Légende</p></span></div></foreignObject></g></g></g></g><g class="nodes"><g transform="translate(532.4975328947369, 86.48258590698242)" data-look="classic" data-et="node" data-node="true" data-id="UserAction" id="flowchart-UserAction-0" class="node default user"><rect stroke="url(#gradient)" height="51" width="189.1875" y="-25.5" x="-94.59375" data-id="UserAction" style="fill:#f9d5e5 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-64.59375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="129.1875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Interaction utilisateur</p></span></div></foreignObject></g></g><g transform="translate(732.3178453947369, 86.48258590698242)" data-look="classic" data-et="node" data-node="true" data-id="DataStorage" id="flowchart-DataStorage-1" class="node default"><path transform="translate(-75.2265625, -38.4825855125078)" style="" class="basic label-container outer-path" d="M0,13.655057008338531 a75.2265625,13.655057008338531 0,0,0 150.453125,0 a75.2265625,13.655057008338531 0,0,0 -150.453125,0 l0,49.65505700833853 a75.2265625,13.655057008338531 0,0,0 150.453125,0 l0,-49.65505700833853"/><g transform="translate(-67.7265625, -3)" style="" class="label"><rect/><foreignObject height="21" width="135.453125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stockage de données</p></span></div></foreignObject></g></g><g transform="translate(341.3334703947369, 86.48258590698242)" data-look="classic" data-et="node" data-node="true" data-id="ServiceAPI" id="flowchart-ServiceAPI-2" class="node default"><rect stroke="url(#gradient)" height="51" width="133.140625" y="-25.5" x="-66.5703125" data-id="ServiceAPI" style="" class="basic label-container"/><g transform="translate(-36.5703125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="73.140625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Service/API</p></span></div></foreignObject></g></g><g transform="translate(1392.3125, 568.4651718139648)" data-look="classic" data-et="node" data-node="true" data-id="User" id="flowchart-User-3" class="node default user"><rect stroke="url(#gradient)" height="51" width="122.25" y="-25.5" x="-61.125" data-id="User" style="fill:#f9d5e5 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-31.125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="62.25"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Utilisateur</p></span></div></foreignObject></g></g><g transform="translate(149.51315789473685, 111.46517181396484)" data-look="classic" data-et="node" data-node="true" data-id="Frontend" id="flowchart-Frontend-4" class="node default frontend"><rect stroke="url(#gradient)" height="51" width="156.5" y="-25.5" x="-78.25" data-id="Frontend" style="fill:#eeeeee !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-48.25, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="96.5"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend React</p></span></div></foreignObject></g></g><g transform="translate(549.25, 568.4651718139648)" data-look="classic" data-et="node" data-node="true" data-id="Backend" id="flowchart-Backend-6" class="node default api"><rect stroke="url(#gradient)" height="51" width="181.4375" y="-25.5" x="-90.71875" data-id="Backend" style="fill:#ffdac1 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-60.71875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="121.4375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend Supabase</p></span></div></foreignObject></g></g><g transform="translate(796.2979910714286, 831.2235374450684)" data-look="classic" data-et="node" data-node="true" data-id="DB" id="flowchart-DB-8" class="node default storage"><path transform="translate(-102.0703125, -41.25836695941135)" style="fill:#c7ceea !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container outer-path" d="M0,15.505577972940896 a102.0703125,15.505577972940896 0,0,0 204.140625,0 a102.0703125,15.505577972940896 0,0,0 -204.140625,0 l0,51.5055779729409 a102.0703125,15.505577972940896 0,0,0 204.140625,0 l0,-51.5055779729409"/><g transform="translate(-94.5703125, -3)" style="" class="label"><rect/><foreignObject height="21" width="189.140625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Base de données PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(1763.6432291666667, 568.4651718139648)" data-look="classic" data-et="node" data-node="true" data-id="CloudinaryAPI" id="flowchart-CloudinaryAPI-16" class="node default api"><rect stroke="url(#gradient)" height="51" width="153.390625" y="-25.5" x="-76.6953125" data-id="CloudinaryAPI" style="fill:#ffdac1 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-46.6953125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="93.390625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Cloudinary</p></span></div></foreignObject></g></g><g transform="translate(1949.8385416666667, 828.0516586303711)" data-look="classic" data-et="node" data-node="true" data-id="CloudinaryStorage" id="flowchart-CloudinaryStorage-18" class="node default storage"><path transform="translate(-72.09375, -38.08648711400046)" style="fill:#c7ceea !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container outer-path" d="M0,13.390991409333642 a72.09375,13.390991409333642 0,0,0 144.1875,0 a72.09375,13.390991409333642 0,0,0 -144.1875,0 l0,49.390991409333644 a72.09375,13.390991409333642 0,0,0 144.1875,0 l0,-49.390991409333644"/><g transform="translate(-64.59375, -3)" style="" class="label"><rect/><foreignObject height="21" width="129.1875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stockage Cloudinary</p></span></div></foreignObject></g></g><g transform="translate(2336.057291666667, 568.4651718139648)" data-look="classic" data-et="node" data-node="true" data-id="PaymentGateway" id="flowchart-PaymentGateway-42" class="node default api"><rect stroke="url(#gradient)" height="51" width="207.875" y="-25.5" x="-103.9375" data-id="PaymentGateway" style="fill:#ffdac1 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-73.9375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="147.875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Passerelle de Paiement</p></span></div></foreignObject></g></g></g><g class="edges edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTM1MS41NjI1LCJ5Ijo1NDIuOTY1MTcxODEzOTY0OH0seyJ4IjoxMzUxLjU2MjUsInkiOjUwNy45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxMTAzLjc2NTYyNSwieSI6NTA3Ljk2NTE3MTgxMzk2NDg0fSx7IngiOjExMDMuNzY1NjI1LCJ5IjozMjkuNDY1MTcxODEzOTY0ODR9LHsieCI6MTM3LjE1Nzg5NDczNjg0MjEsInkiOjMyOS40NjUxNzE4MTM5NjQ4NH0seyJ4IjoxMzcuMTU3ODk0NzM2ODQyMSwieSI6MTM2Ljk2NTE3MTgxMzk2NDg0fV0=" data-id="L_User_Frontend_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_User_Frontend_0_0" d="M1351.5625,542.9651718139648L1351.5625,515.0362396258304Q1351.5625,507.96517181396484 1344.4914321881345,507.96517181396484L1110.8366928118655,507.96517181396484Q1103.765625,507.96517181396484 1103.765625,500.89410400209937L1103.765625,336.5362396258303Q1103.765625,329.46517181396484 1096.6945571881345,329.46517181396484L144.22896254870759,329.46517181396484Q137.1578947368421,329.46517181396484 137.1578947368421,322.39410400209937L137.1578947368421,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NzkuNSwieSI6MTM2Ljk2NTE3MTgxMzk2NDg0fSx7IngiOjc5LjUsInkiOjUyNS40NjUxNzE4MTM5NjQ4fSx7IngiOjQ4MS4yMTA5Mzc1LCJ5Ijo1MjUuNDY1MTcxODEzOTY0OH0seyJ4Ijo0ODEuMjEwOTM3NSwieSI6NTQyLjk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Frontend_Backend_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_Backend_0_0" d="M79.5,136.96517181396484L79.5,518.3941040020993Q79.5,525.4651718139648 86.57106781186548,525.4651718139648L474.4609375,525.4651718139648Q481.2109375,525.4651718139648 481.2109375,532.2151718139648L481.2109375,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NDg0LjQ1MDg5Mjg1NzE0MjgzLCJ5Ijo1OTMuOTY1MTcxODEzOTY0OH0seyJ4Ijo0ODQuNDUwODkyODU3MTQyOSwieSI6NzU0Ljk2NTE3MTgxMzk2NDh9LHsieCI6NzIzLjM5MDYyNSwieSI6NzU0Ljk2NTE3MTgxMzk2NDh9LHsieCI6NzIzLjM5MDYyNSwieSI6Nzg5Ljk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Backend_DB_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_DB_0_0" d="M484.45089285714283,593.9651718139648L484.4508928571429,747.8941040020993Q484.4508928571429,754.9651718139648 491.52196066900837,754.9651718139648L716.3195571881345,754.9651718139648Q723.390625,754.9651718139648 723.390625,762.0362396258304L723.390625,785.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTM3MS45Mzc1LCJ5Ijo1NDIuOTY1MTcxODEzOTY0OH0seyJ4IjoxMzcxLjkzNzUsInkiOjQ5MC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoxMjQxLjE3MTg3NSwieSI6NDkwLjQ2NTE3MTgxMzk2NDg0fSx7IngiOjEyNDEuMTcxODc1LCJ5IjozMTEuOTY1MTcxODEzOTY0ODR9LHsieCI6MTQ1LjM5NDczNjg0MjEwNTI2LCJ5IjozMTEuOTY1MTcxODEzOTY0ODR9LHsieCI6MTQ1LjM5NDczNjg0MjEwNTI2LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9XQ==" data-id="L_User_Frontend_2_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_User_Frontend_2_0" d="M1371.9375,542.9651718139648L1371.9375,497.5362396258303Q1371.9375,490.46517181396484 1364.8664321881345,490.46517181396484L1248.2429428118655,490.46517181396484Q1241.171875,490.46517181396484 1241.171875,483.39410400209937L1241.171875,319.0362396258303Q1241.171875,311.96517181396484 1234.1008071881345,311.96517181396484L152.46580465397074,311.96517181396484Q145.39473684210526,311.96517181396484 145.39473684210526,304.89410400209937L145.39473684210526,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6ODcuNzM2ODQyMTA1MjYzMTUsInkiOjEzNi45NjUxNzE4MTM5NjQ4NH0seyJ4Ijo4Ny43MzY4NDIxMDUyNjMxNSwieSI6NDM0LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjI0OC42NDA2MjUsInkiOjQzNC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoyNDguNjQwNjI1LCJ5Ijo1MDcuOTY1MTcxODEzOTY0ODR9LHsieCI6NTAzLjg5MDYyNSwieSI6NTA3Ljk2NTE3MTgxMzk2NDg0fSx7IngiOjUwMy44OTA2MjUsInkiOjU0Mi45NjUxNzE4MTM5NjQ4fV0=" data-id="L_Frontend_Backend_2_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_Backend_2_0" d="M87.73684210526315,136.96517181396484L87.73684210526315,427.39410400209937Q87.73684210526315,434.46517181396484 94.80790991712863,434.46517181396484L241.56955718813452,434.46517181396484Q248.640625,434.46517181396484 248.640625,441.5362396258303L248.640625,500.89410400209937Q248.640625,507.96517181396484 255.71169281186548,507.96517181396484L496.8195571881345,507.96517181396484Q503.890625,507.96517181396484 503.890625,515.0362396258304L503.890625,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NTEwLjM3MDUzNTcxNDI4NTcsInkiOjU5My45NjUxNzE4MTM5NjQ4fSx7IngiOjUxMC4zNzA1MzU3MTQyODU3LCJ5Ijo2ODEuNDY1MTcxODEzOTY0OH0seyJ4Ijo2MzMuMTg1MjY3ODU3MTQyOSwieSI6NjgxLjQ2NTE3MTgxMzk2NDh9LHsieCI6NjMzLjE4NTI2Nzg1NzE0MjksInkiOjczNy40NjUxNzE4MTM5NjQ4fSx7IngiOjc1Mi41NTM1NzE0Mjg1NzE0LCJ5Ijo3MzcuNDY1MTcxODEzOTY0OH0seyJ4Ijo3NTIuNTUzNTcxNDI4NTcxNCwieSI6Nzg5Ljk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Backend_DB_2_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_DB_2_0" d="M510.3705357142857,593.9651718139648L510.3705357142857,674.3941040020993Q510.3705357142857,681.4651718139648 517.4416035261512,681.4651718139648L626.1142000452774,681.4651718139648Q633.1852678571429,681.4651718139648 633.1852678571429,688.5362396258304L633.1852678571429,730.3941040020993Q633.1852678571429,737.4651718139648 640.2563356690084,737.4651718139648L745.482503616706,737.4651718139648Q752.5535714285714,737.4651718139648 752.5535714285714,744.5362396258304L752.5535714285714,785.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTc4LjM0MjEwNTI2MzE1Nzg2LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTc4LjM0MjEwNTI2MzE1Nzg2LCJ5IjoyNDEuOTY1MTcxODEzOTY0ODR9LHsieCI6MTczOC4wNzgxMjUsInkiOjI0MS45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxNzM4LjA3ODEyNSwieSI6NTQyLjk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Frontend_CloudinaryAPI_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_CloudinaryAPI_0_0" d="M178.34210526315786,136.96517181396484L178.34210526315786,234.89410400209937Q178.34210526315786,241.96517181396484 185.41317307502334,241.96517181396484L1731.0070571881345,241.96517181396484Q1738.078125,241.96517181396484 1738.078125,249.03623962583032L1738.078125,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTc2My42NDMyMjkxNjY2NjY3LCJ5Ijo1OTMuOTY1MTcxODEzOTY0OH0seyJ4IjoxNzYzLjY0MzIyOTE2NjY2NjcsInkiOjczNy40NjUxNzE4MTM5NjQ4fSx7IngiOjE5MTMuNzkxNjY2NjY2NjY2NywieSI6NzM3LjQ2NTE3MTgxMzk2NDh9LHsieCI6MTkxMy43OTE2NjY2NjY2NjY3LCJ5Ijo3ODkuOTY1MTcxODEzOTY0OH1d" data-id="L_CloudinaryAPI_CloudinaryStorage_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudinaryAPI_CloudinaryStorage_0_0" d="M1763.6432291666667,593.9651718139648L1763.6432291666667,730.3941040020993Q1763.6432291666667,737.4651718139648 1770.7142969785323,737.4651718139648L1906.7205988548012,737.4651718139648Q1913.7916666666667,737.4651718139648 1913.7916666666667,744.5362396258304L1913.7916666666667,785.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTc4OS4yMDgzMzMzMzMzMzM1LCJ5Ijo1NDIuOTY1MTcxODEzOTY0OH0seyJ4IjoxNzg5LjIwODMzMzMzMzMzMzUsInkiOjQ5MC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoxODYzLjk2ODc1LCJ5Ijo0OTAuNDY1MTcxODEzOTY0ODR9LHsieCI6MTg2My45Njg3NSwieSI6MjI0LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjE4Ni41Nzg5NDczNjg0MjEsInkiOjIyNC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoxODYuNTc4OTQ3MzY4NDIxLCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9XQ==" data-id="L_CloudinaryAPI_Frontend_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudinaryAPI_Frontend_0_0" d="M1789.2083333333335,542.9651718139648L1789.2083333333335,497.5362396258303Q1789.2083333333335,490.46517181396484 1796.279401145199,490.46517181396484L1856.8976821881345,490.46517181396484Q1863.96875,490.46517181396484 1863.96875,483.39410400209937L1863.96875,231.53623962583032Q1863.96875,224.46517181396484 1856.8976821881345,224.46517181396484L193.6500151802865,224.46517181396484Q186.578947368421,224.46517181396484 186.578947368421,217.39410400209937L186.578947368421,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6OTUuOTczNjg0MjEwNTI2MzIsInkiOjEzNi45NjUxNzE4MTM5NjQ4NH0seyJ4Ijo5NS45NzM2ODQyMTA1MjYzMiwieSI6NDE2Ljk2NTE3MTgxMzk2NDg0fSx7IngiOjQwOS4wMzEyNSwieSI6NDE2Ljk2NTE3MTgxMzk2NDg0fSx7IngiOjQwOS4wMzEyNSwieSI6NDkwLjQ2NTE3MTgxMzk2NDg0fSx7IngiOjUyNi41NzAzMTI1LCJ5Ijo0OTAuNDY1MTcxODEzOTY0ODR9LHsieCI6NTI2LjU3MDMxMjUsInkiOjU0Mi45NjUxNzE4MTM5NjQ4fV0=" data-id="L_Frontend_Backend_3_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_Backend_3_0" d="M95.97368421052632,136.96517181396484L95.97368421052632,409.89410400209937Q95.97368421052632,416.96517181396484 103.04475202239179,416.96517181396484L401.9601821881345,416.96517181396484Q409.03125,416.96517181396484 409.03125,424.0362396258303L409.03125,483.39410400209937Q409.03125,490.46517181396484 416.1023178118655,490.46517181396484L519.4992446881345,490.46517181396484Q526.5703125,490.46517181396484 526.5703125,497.5362396258303L526.5703125,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NTM2LjI5MDE3ODU3MTQyODYsInkiOjU5My45NjUxNzE4MTM5NjQ4fSx7IngiOjUzNi4yOTAxNzg1NzE0Mjg2LCJ5Ijo2NjMuOTY1MTcxODEzOTY0OH0seyJ4Ijo3ODEuNzE2NTE3ODU3MTQyOSwieSI6NjYzLjk2NTE3MTgxMzk2NDh9LHsieCI6NzgxLjcxNjUxNzg1NzE0MjksInkiOjc4OS45NjUxNzE4MTM5NjQ4fV0=" data-id="L_Backend_DB_3_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_DB_3_0" d="M536.2901785714286,593.9651718139648L536.2901785714286,656.8941040020993Q536.2901785714286,663.9651718139648 543.361246383294,663.9651718139648L774.6454500452774,663.9651718139648Q781.7165178571429,663.9651718139648 781.7165178571429,671.0362396258304L781.7165178571429,785.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTA0LjIxMDUyNjMxNTc4OTQ4LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTA0LjIxMDUyNjMxNTc4OTQ4LCJ5IjozOTkuNDY1MTcxODEzOTY0ODR9LHsieCI6NTQ5LjI1LCJ5IjozOTkuNDY1MTcxODEzOTY0ODR9LHsieCI6NTQ5LjI1LCJ5Ijo1NDIuOTY1MTcxODEzOTY0OH1d" data-id="L_Frontend_Backend_4_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_Backend_4_0" d="M104.21052631578948,136.96517181396484L104.21052631578948,392.39410400209937Q104.21052631578948,399.46517181396484 111.28159412765496,399.46517181396484L542.1789321881345,399.46517181396484Q549.25,399.46517181396484 549.25,406.5362396258303L549.25,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NTYyLjIwOTgyMTQyODU3MTQsInkiOjU5My45NjUxNzE4MTM5NjQ4fSx7IngiOjU2Mi4yMDk4MjE0Mjg1NzE0LCJ5Ijo2NDYuNDY1MTcxODEzOTY0OH0seyJ4Ijo5MTYuNDE5NjQyODU3MTQyOSwieSI6NjQ2LjQ2NTE3MTgxMzk2NDh9LHsieCI6OTE2LjQxOTY0Mjg1NzE0MjksInkiOjczNy40NjUxNzE4MTM5NjQ4fSx7IngiOjgxMC44Nzk0NjQyODU3MTQyLCJ5Ijo3MzcuNDY1MTcxODEzOTY0OH0seyJ4Ijo4MTAuODc5NDY0Mjg1NzE0MiwieSI6Nzg5Ljk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Backend_DB_4_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_DB_4_0" d="M562.2098214285714,593.9651718139648L562.2098214285714,639.3941040020993Q562.2098214285714,646.4651718139648 569.2808892404369,646.4651718139648L909.3485750452774,646.4651718139648Q916.4196428571429,646.4651718139648 916.4196428571429,653.5362396258304L916.4196428571429,730.3941040020993Q916.4196428571429,737.4651718139648 909.3485750452774,737.4651718139648L817.9505320975798,737.4651718139648Q810.8794642857142,737.4651718139648 810.8794642857142,744.5362396258304L810.8794642857142,785.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6ODQwLjA0MjQxMDcxNDI4NTgsInkiOjc4OS45NjUxNzE4MTM5NjQ4fSx7IngiOjg0MC4wNDI0MTA3MTQyODU4LCJ5Ijo3NTQuOTY1MTcxODEzOTY0OH0seyJ4IjoxMDc4LjIzMjE0Mjg1NzE0MywieSI6NzU0Ljk2NTE3MTgxMzk2NDh9LHsieCI6MTA3OC4yMzIxNDI4NTcxNDMsInkiOjYyOC45NjUxNzE4MTM5NjQ4fSx7IngiOjU4OC4xMjk0NjQyODU3MTQyLCJ5Ijo2MjguOTY1MTcxODEzOTY0OH0seyJ4Ijo1ODguMTI5NDY0Mjg1NzE0MiwieSI6NTkzLjk2NTE3MTgxMzk2NDh9XQ==" data-id="L_DB_Backend_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB_Backend_0_0" d="M840.0424107142858,789.9651718139648L840.0424107142858,762.0362396258304Q840.0424107142858,754.9651718139648 847.1134785261513,754.9651718139648L1071.1610750452774,754.9651718139648Q1078.232142857143,754.9651718139648 1078.232142857143,747.8941040020993L1078.232142857143,636.0362396258304Q1078.232142857143,628.9651718139648 1071.1610750452774,628.9651718139648L595.2005320975798,628.9651718139648Q588.1294642857142,628.9651718139648 588.1294642857142,621.8941040020993L588.1294642857142,597.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NTcxLjkyOTY4NzUsInkiOjU0Mi45NjUxNzE4MTM5NjQ4fSx7IngiOjU3MS45Mjk2ODc1LCJ5Ijo0OTAuNDY1MTcxODEzOTY0ODR9LHsieCI6NzE2LjQ1MzEyNSwieSI6NDkwLjQ2NTE3MTgxMzk2NDg0fSx7IngiOjcxNi40NTMxMjUsInkiOjM4MS45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxMTIuNDQ3MzY4NDIxMDUyNjMsInkiOjM4MS45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxMTIuNDQ3MzY4NDIxMDUyNjMsInkiOjEzNi45NjUxNzE4MTM5NjQ4NH1d" data-id="L_Backend_Frontend_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_Frontend_0_0" d="M571.9296875,542.9651718139648L571.9296875,497.5362396258303Q571.9296875,490.46517181396484 579.0007553118655,490.46517181396484L709.3820571881345,490.46517181396484Q716.453125,490.46517181396484 716.453125,483.39410400209937L716.453125,389.0362396258303Q716.453125,381.96517181396484 709.3820571881345,381.96517181396484L119.5184362329181,381.96517181396484Q112.44736842105263,381.96517181396484 112.44736842105263,374.89410400209937L112.44736842105263,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTk0LjgxNTc4OTQ3MzY4NDE2LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTk0LjgxNTc4OTQ3MzY4NDE2LCJ5IjoyMDYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTk0OS44Mzg1NDE2NjY2NjY3LCJ5IjoyMDYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTk0OS44Mzg1NDE2NjY2NjY3LCJ5Ijo0NjIuNDY1MTcxODEzOTY0ODR9LHsieCI6MTk0OS44Mzg1NDE2NjY2NjY3LCJ5Ijo3MDkuNDY1MTcxODEzOTY0OH0seyJ4IjoxOTQ5LjgzODU0MTY2NjY2NjcsInkiOjc4OS45NjUxNzE4MTM5NjQ4fV0=" data-id="L_Frontend_CloudinaryStorage_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_CloudinaryStorage_0_0" d="M194.81578947368416,136.96517181396484L194.81578947368416,199.89410400209937Q194.81578947368416,206.96517181396484 201.88685728554964,206.96517181396484L1942.7674738548012,206.96517181396484Q1949.8385416666667,206.96517181396484 1949.8385416666667,214.03623962583032L1949.8385416666667,462.46517181396484L1949.8385416666667,709.4651718139648L1949.8385416666667,785.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTk4NS44ODU0MTY2NjY2NjY3LCJ5Ijo3ODkuOTY1MTcxODEzOTY0OH0seyJ4IjoxOTg1Ljg4NTQxNjY2NjY2NjcsInkiOjczNy40NjUxNzE4MTM5NjQ4fSx7IngiOjIxMTkuNDc5MTY2NjY2NjY3LCJ5Ijo3MzcuNDY1MTcxODEzOTY0OH0seyJ4IjoyMTE5LjQ3OTE2NjY2NjY2NywieSI6NzA5LjQ2NTE3MTgxMzk2NDh9LHsieCI6MjExOS40NzkxNjY2NjY2NjcsInkiOjQ2Mi40NjUxNzE4MTM5NjQ4NH0seyJ4IjoyMTE5LjQ3OTE2NjY2NjY2NywieSI6MTg5LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjIwMy4wNTI2MzE1Nzg5NDczLCJ5IjoxODkuNDY1MTcxODEzOTY0ODR9LHsieCI6MjAzLjA1MjYzMTU3ODk0NzMsInkiOjEzNi45NjUxNzE4MTM5NjQ4NH1d" data-id="L_CloudinaryStorage_Frontend_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudinaryStorage_Frontend_0_0" d="M1985.8854166666667,789.9651718139648L1985.8854166666667,744.5362396258304Q1985.8854166666667,737.4651718139648 1992.9564844785323,737.4651718139648L2112.4080988548017,737.4651718139648Q2119.479166666667,737.4651718139648 2119.479166666667,730.3941040020993L2119.479166666667,709.4651718139648L2119.479166666667,462.46517181396484L2119.479166666667,196.53623962583032Q2119.479166666667,189.46517181396484 2112.4080988548017,189.46517181396484L210.1236993908128,189.46517181396484Q203.0526315789473,189.46517181396484 203.0526315789473,182.39410400209937L203.0526315789473,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTUzLjYzMTU3ODk0NzM2ODQsInkiOjEzNi45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxNTMuNjMxNTc4OTQ3MzY4NCwieSI6Mjk0LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjEzOTIuMzEyNSwieSI6Mjk0LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjEzOTIuMzEyNSwieSI6NTQyLjk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Frontend_User_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_User_0_0" d="M153.6315789473684,136.96517181396484L153.6315789473684,287.39410400209937Q153.6315789473684,294.46517181396484 160.7026467592339,294.46517181396484L1385.2414321881345,294.46517181396484Q1392.3125,294.46517181396484 1392.3125,301.5362396258303L1392.3125,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTQxMi42ODc1LCJ5Ijo1NDIuOTY1MTcxODEzOTY0OH0seyJ4IjoxNDEyLjY4NzUsInkiOjQ5MC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoxNTA0LjY0MDYyNSwieSI6NDkwLjQ2NTE3MTgxMzk2NDg0fSx7IngiOjE1MDQuNjQwNjI1LCJ5IjoyNzYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTYxLjg2ODQyMTA1MjYzMTU2LCJ5IjoyNzYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTYxLjg2ODQyMTA1MjYzMTU2LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9XQ==" data-id="L_User_Frontend_3_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_User_Frontend_3_0" d="M1412.6875,542.9651718139648L1412.6875,497.5362396258303Q1412.6875,490.46517181396484 1419.7585678118655,490.46517181396484L1497.5695571881345,490.46517181396484Q1504.640625,490.46517181396484 1504.640625,483.39410400209937L1504.640625,284.0362396258303Q1504.640625,276.96517181396484 1497.5695571881345,276.96517181396484L168.93948886449704,276.96517181396484Q161.86842105263156,276.96517181396484 161.86842105263156,269.89410400209937L161.86842105263156,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MjExLjI4OTQ3MzY4NDIxMDQ2LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9LHsieCI6MjExLjI4OTQ3MzY4NDIxMDQ2LCJ5IjoxNzEuOTY1MTcxODEzOTY0ODR9LHsieCI6MjMwMS40MTE0NTgzMzMzMzM1LCJ5IjoxNzEuOTY1MTcxODEzOTY0ODR9LHsieCI6MjMwMS40MTE0NTgzMzMzMzM1LCJ5Ijo1NDIuOTY1MTcxODEzOTY0OH1d" data-id="L_Frontend_PaymentGateway_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_PaymentGateway_0_0" d="M211.28947368421046,136.96517181396484L211.28947368421046,164.89410400209937Q211.28947368421046,171.96517181396484 218.36054149607594,171.96517181396484L2294.340390521468,171.96517181396484Q2301.4114583333335,171.96517181396484 2301.4114583333335,179.03623962583032L2301.4114583333335,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MjM3MC43MDMxMjUwMDAwMDA1LCJ5Ijo1NDIuOTY1MTcxODEzOTY0OH0seyJ4IjoyMzcwLjcwMzEyNTAwMDAwMDUsInkiOjQ5MC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoyNDI2LjM2NDU4MzMzMzMzMzUsInkiOjQ5MC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoyNDI2LjM2NDU4MzMzMzMzMzUsInkiOjE1NC40NjUxNzE4MTM5NjQ4NH0seyJ4IjoyMTkuNTI2MzE1Nzg5NDczNiwieSI6MTU0LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjIxOS41MjYzMTU3ODk0NzM2LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9XQ==" data-id="L_PaymentGateway_Frontend_0_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PaymentGateway_Frontend_0_0" d="M2370.7031250000005,542.9651718139648L2370.7031250000005,497.5362396258303Q2370.7031250000005,490.46517181396484 2377.7741928118658,490.46517181396484L2419.293515521468,490.46517181396484Q2426.3645833333335,490.46517181396484 2426.3645833333335,483.39410400209937L2426.3645833333335,161.53623962583032Q2426.3645833333335,154.46517181396484 2419.293515521468,154.46517181396484L226.2763157894736,154.46517181396484Q219.5263157894736,154.46517181396484 219.5263157894736,147.71517181396484L219.5263157894736,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTIwLjY4NDIxMDUyNjMxNTc4LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTIwLjY4NDIxMDUyNjMxNTc4LCJ5IjozNjQuNDY1MTcxODEzOTY0ODR9LHsieCI6ODYxLjM3NSwieSI6MzY0LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjg2MS4zNzUsInkiOjUwNy45NjUxNzE4MTM5NjQ4NH0seyJ4Ijo1OTQuNjA5Mzc1LCJ5Ijo1MDcuOTY1MTcxODEzOTY0ODR9LHsieCI6NTk0LjYwOTM3NSwieSI6NTQyLjk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Frontend_Backend_5_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_Backend_5_0" d="M120.68421052631578,136.96517181396484L120.68421052631578,357.39410400209937Q120.68421052631578,364.46517181396484 127.75527833818126,364.46517181396484L854.3039321881345,364.46517181396484Q861.375,364.46517181396484 861.375,371.5362396258303L861.375,500.89410400209937Q861.375,507.96517181396484 854.3039321881345,507.96517181396484L601.6804428118655,507.96517181396484Q594.609375,507.96517181396484 594.609375,515.0362396258304L594.609375,538.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NjE0LjA0OTEwNzE0Mjg1NzEsInkiOjU5My45NjUxNzE4MTM5NjQ4fSx7IngiOjYxNC4wNDkxMDcxNDI4NTcxLCJ5Ijo2MTEuNDY1MTcxODEzOTY0OH0seyJ4IjoxMjI2LjY1NDAxNzg1NzE0MywieSI6NjExLjQ2NTE3MTgxMzk2NDh9LHsieCI6MTIyNi42NTQwMTc4NTcxNDMsInkiOjc3Mi40NjUxNzE4MTM5NjQ4fSx7IngiOjg2OS4yMDUzNTcxNDI4NTcxLCJ5Ijo3NzIuNDY1MTcxODEzOTY0OH0seyJ4Ijo4NjkuMjA1MzU3MTQyODU3MSwieSI6Nzg5Ljk2NTE3MTgxMzk2NDh9XQ==" data-id="L_Backend_DB_5_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_DB_5_0" d="M614.0491071428571,593.9651718139648L614.0491071428571,604.3941040020993Q614.0491071428571,611.4651718139648 621.1201749547226,611.4651718139648L1219.5829500452774,611.4651718139648Q1226.654017857143,611.4651718139648 1226.654017857143,618.5362396258304L1226.654017857143,765.3941040020993Q1226.654017857143,772.4651718139648 1219.5829500452774,772.4651718139648L875.9553571428571,772.4651718139648Q869.2053571428571,772.4651718139648 869.2053571428571,779.2151718139648L869.2053571428571,785.9651718139648"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NjE3LjI4OTA2MjUsInkiOjU0Mi45NjUxNzE4MTM5NjQ4fSx7IngiOjYxNy4yODkwNjI1LCJ5Ijo1MjUuNDY1MTcxODEzOTY0OH0seyJ4Ijo5ODAuNDIxODc1LCJ5Ijo1MjUuNDY1MTcxODEzOTY0OH0seyJ4Ijo5ODAuNDIxODc1LCJ5IjozNDYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTI4LjkyMTA1MjYzMTU3ODk2LCJ5IjozNDYuOTY1MTcxODEzOTY0ODR9LHsieCI6MTI4LjkyMTA1MjYzMTU3ODk2LCJ5IjoxMzYuOTY1MTcxODEzOTY0ODR9XQ==" data-id="L_Backend_Frontend_2_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_Frontend_2_0" d="M617.2890625,542.9651718139648L617.2890625,532.5362396258304Q617.2890625,525.4651718139648 624.3601303118655,525.4651718139648L973.3508071881345,525.4651718139648Q980.421875,525.4651718139648 980.421875,518.3941040020993L980.421875,354.0362396258303Q980.421875,346.96517181396484 973.3508071881345,346.96517181396484L135.99212044344443,346.96517181396484Q128.92105263157896,346.96517181396484 128.92105263157896,339.89410400209937L128.92105263157896,140.96517181396484"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTcwLjEwNTI2MzE1Nzg5NDcsInkiOjEzNi45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxNzAuMTA1MjYzMTU3ODk0NywieSI6MjU5LjQ2NTE3MTgxMzk2NDg0fSx7IngiOjE2MTcuMjAzMTI1LCJ5IjoyNTkuNDY1MTcxODEzOTY0ODR9LHsieCI6MTYxNy4yMDMxMjUsInkiOjUwNy45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxNDMzLjA2MjUsInkiOjUwNy45NjUxNzE4MTM5NjQ4NH0seyJ4IjoxNDMzLjA2MjUsInkiOjU0Mi45NjUxNzE4MTM5NjQ4fV0=" data-id="L_Frontend_User_2_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Frontend_User_2_0" d="M170.1052631578947,136.96517181396484L170.1052631578947,252.39410400209937Q170.1052631578947,259.46517181396484 177.1763309697602,259.46517181396484L1610.1320571881345,259.46517181396484Q1617.203125,259.46517181396484 1617.203125,266.5362396258303L1617.203125,500.89410400209937Q1617.203125,507.96517181396484 1610.1320571881345,507.96517181396484L1440.1335678118655,507.96517181396484Q1433.0625,507.96517181396484 1433.0625,515.0362396258304L1433.0625,538.9651718139648"/></g><g class="edgeLabels"><g transform="translate(1103.46875, 462.46517181396484)" class="edgeLabel"><g transform="translate(-45.703125, -10.5)" data-id="L_User_Frontend_0_0" class="label"><foreignObject height="21" width="91.40625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Accède à l'app</p></span></div></foreignObject></g></g><g transform="translate(79.3203125, 462.46517181396484)" class="edgeLabel"><g transform="translate(-67.3203125, -10.5)" data-id="L_Frontend_Backend_0_0" class="label"><foreignObject height="21" width="134.640625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Inscription/Connexion</p></span></div></foreignObject></g></g><g transform="translate(484.3180803571429, 709.4651718139648)" class="edgeLabel"><g transform="translate(-58.3671875, -10.5)" data-id="L_Backend_DB_0_0" class="label"><foreignObject height="21" width="116.734375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Gère auth &amp; profils</p></span></div></foreignObject></g></g><g transform="translate(1240.9921875, 462.46517181396484)" class="edgeLabel"><g transform="translate(-74.3203125, -10.5)" data-id="L_User_Frontend_2_0" class="label"><foreignObject height="21" width="148.640625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Crée/modifie campagne</p></span></div></foreignObject></g></g><g transform="translate(248.5859375, 462.46517181396484)" class="edgeLabel"><g transform="translate(-84.4453125, -10.5)" data-id="L_Frontend_Backend_2_0" class="label"><foreignObject height="21" width="168.890625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Soumet données textuelles</p></span></div></foreignObject></g></g><g transform="translate(632.9508928571429, 709.4651718139648)" class="edgeLabel"><g transform="translate(-72.765625, -10.5)" data-id="L_Backend_DB_2_0" class="label"><foreignObject height="21" width="145.53125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Stocke infos campagne</p></span></div></foreignObject></g></g><g transform="translate(1737.7734375, 462.46517181396484)" class="edgeLabel"><g transform="translate(-43.1953125, -10.5)" data-id="L_Frontend_CloudinaryAPI_0_0" class="label"><foreignObject height="21" width="86.390625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Upload média</p></span></div></foreignObject></g></g><g transform="translate(1763.5572916666667, 709.4651718139648)" class="edgeLabel"><g transform="translate(-42.4140625, -10.5)" data-id="L_CloudinaryAPI_CloudinaryStorage_0_0" class="label"><foreignObject height="21" width="84.828125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Stocke média</p></span></div></foreignObject></g></g><g transform="translate(1863.96875, 462.46517181396484)" class="edgeLabel"><g transform="translate(-65.5, -10.5)" data-id="L_CloudinaryAPI_Frontend_0_0" class="label"><foreignObject height="21" width="131"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Retourne URL média</p></span></div></foreignObject></g></g><g transform="translate(408.640625, 462.46517181396484)" class="edgeLabel"><g transform="translate(-58.109375, -10.5)" data-id="L_Frontend_Backend_3_0" class="label"><foreignObject height="21" width="116.21875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Envoie URL média</p></span></div></foreignObject></g></g><g transform="translate(781.3180803571429, 709.4651718139648)" class="edgeLabel"><g transform="translate(-58.1015625, -10.5)" data-id="L_Backend_DB_3_0" class="label"><foreignObject height="21" width="116.203125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Stocke URL média</p></span></div></foreignObject></g></g><g transform="translate(548.8515625, 462.46517181396484)" class="edgeLabel"><g transform="translate(-64.6015625, -10.5)" data-id="L_Frontend_Backend_4_0" class="label"><foreignObject height="21" width="129.203125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Requête campagnes</p></span></div></foreignObject></g></g><g transform="translate(916.0758928571429, 709.4651718139648)" class="edgeLabel"><g transform="translate(-59.15625, -10.5)" data-id="L_Backend_DB_4_0" class="label"><foreignObject height="21" width="118.3125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Récupère données</p></span></div></foreignObject></g></g><g transform="translate(1077.943080357143, 709.4651718139648)" class="edgeLabel"><g transform="translate(-85.2109375, -10.5)" data-id="L_DB_Backend_0_0" class="label"><foreignObject height="21" width="170.421875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Données avec URLs média</p></span></div></foreignObject></g></g><g transform="translate(716.1640625, 462.46517181396484)" class="edgeLabel"><g transform="translate(-85.2109375, -10.5)" data-id="L_Backend_Frontend_0_0" class="label"><foreignObject height="21" width="170.421875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Données avec URLs média</p></span></div></foreignObject></g></g><g transform="translate(1949.6588541666667, 568.4651718139648)" class="edgeLabel"><g transform="translate(-74.3203125, -10.5)" data-id="L_Frontend_CloudinaryStorage_0_0" class="label"><foreignObject height="21" width="148.640625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Requête média via URL</p></span></div></foreignObject></g></g><g transform="translate(2119.299479166667, 568.4651718139648)" class="edgeLabel"><g transform="translate(-77.8203125, -10.5)" data-id="L_CloudinaryStorage_Frontend_0_0" class="label"><foreignObject height="21" width="155.640625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Retourne média optimisé</p></span></div></foreignObject></g></g><g transform="translate(1392.2265625, 462.46517181396484)" class="edgeLabel"><g transform="translate(-59.4140625, -10.5)" data-id="L_Frontend_User_0_0" class="label"><foreignObject height="21" width="118.828125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Affiche campagnes</p></span></div></foreignObject></g></g><g transform="translate(1504.171875, 462.46517181396484)" class="edgeLabel"><g transform="translate(-35.03125, -10.5)" data-id="L_User_Frontend_3_0" class="label"><foreignObject height="21" width="70.0625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Fait un don</p></span></div></foreignObject></g></g><g transform="translate(2301.3880208333335, 462.46517181396484)" class="edgeLabel"><g transform="translate(-50.9765625, -10.5)" data-id="L_Frontend_PaymentGateway_0_0" class="label"><foreignObject height="21" width="101.953125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Initie transaction</p></span></div></foreignObject></g></g><g transform="translate(2425.8958333333335, 462.46517181396484)" class="edgeLabel"><g transform="translate(-56.03125, -10.5)" data-id="L_PaymentGateway_Frontend_0_0" class="label"><foreignObject height="21" width="112.0625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Process paiement</p></span></div></foreignObject></g></g><g transform="translate(860.8984375, 462.46517181396484)" class="edgeLabel"><g transform="translate(-42.0234375, -10.5)" data-id="L_Frontend_Backend_5_0" class="label"><foreignObject height="21" width="84.046875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Confirme don</p></span></div></foreignObject></g></g><g transform="translate(1226.185267857143, 709.4651718139648)" class="edgeLabel"><g transform="translate(-45.53125, -10.5)" data-id="L_Backend_DB_5_0" class="label"><foreignObject height="21" width="91.0625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Enregistre don</p></span></div></foreignObject></g></g><g transform="translate(980.34375, 462.46517181396484)" class="edgeLabel"><g transform="translate(-59.921875, -10.5)" data-id="L_Backend_Frontend_2_0" class="label"><foreignObject height="21" width="119.84375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Notifie événements</p></span></div></foreignObject></g></g><g transform="translate(1616.890625, 462.46517181396484)" class="edgeLabel"><g transform="translate(-60.1875, -10.5)" data-id="L_Frontend_User_2_0" class="label"><foreignObject height="21" width="120.375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Affiche notifications</p></span></div></foreignObject></g></g></g></svg>