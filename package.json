{"name": "africagnotte", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "patch-package", "cloudinary:test": "node scripts/test-cloudinary-config.js", "cloudinary:verify": "node scripts/verify-preset-creation.js", "cloudinary:create": "node scripts/create-cloudinary-preset.js", "admin:setup": "node scripts/setup-admin-neon.js", "admin:create-user": "node scripts/setup-admin-neon.js create-admin", "admin:create-test-users": "node scripts/create-test-users.js", "admin:create-custom-user": "node scripts/create-test-users.js custom", "admin:create-complete-users": "node scripts/create-complete-users.js", "admin:check-users": "node scripts/create-complete-users.js check"}, "dependencies": {"@cloudinary/url-gen": "^1.21.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tabler/icons-react": "^3.31.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^12.19.1", "install": "^0.13.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.509.0", "next": "15.3.1", "next-cloudinary": "^6.16.0", "node-fetch": "^3.3.2", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.1", "patch-package": "^8.0.0", "postcss": "^8.4.32", "typescript": "^5"}}